package com.gnico.majo.infrastructure.routes

import com.gnico.majo.adapter.controller.rest.ProductoController
import com.gnico.majo.adapter.controller.dto.ErrorResponse
import com.gnico.majo.adapter.controller.dto.ProductoRequest
import com.gnico.majo.adapter.controller.dto.ProductoUpdateRequest
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.response.respondText
import io.ktor.server.routing.delete
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.routing

fun Application.configureProductoRoutes(productoController: ProductoController) {
    routing {
        // Test
        get("/hello") {
            call.respondText("Hello, K<PERSON>!")
        }


        // Obtener todos los productos
        get("/productos") {
            try {
                val response = productoController.getAllProductos()
                call.respond(HttpStatusCode.OK, response)
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, ErrorResponse(e.message ?: "Error interno del servidor"))
            }
        }

        // Obtener un producto por código
        get("/productos/{codigo}") {
            try {
                val codigo = call.parameters["codigo"]?.toIntOrNull()
                    ?: return@get call.respond(HttpStatusCode.BadRequest, ErrorResponse("Código inválido"))

                val producto = productoController.getProductoByCodigo(codigo)
                if (producto != null) {
                    call.respond(HttpStatusCode.OK, producto)
                } else {
                    call.respond(HttpStatusCode.NotFound, ErrorResponse("Producto no encontrado"))
                }
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, ErrorResponse(e.message ?: "Error interno del servidor"))
            }
        }



        // Crear un nuevo producto
        post("/productos") {
            try {
                val request = call.receive<ProductoRequest>()
                val response = productoController.createProducto(request)
                call.respond(HttpStatusCode.Created, response)
            } catch (e: IllegalArgumentException) {
                call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Solicitud inválida"))
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, ErrorResponse(e.message ?: "Error interno del servidor"))
            }
        }

        // Actualizar un producto existente
        put("/productos") {
            try {
                val request = call.receive<ProductoUpdateRequest>()
                val success = productoController.updateProducto(request)
                if (success) {
                    call.respond(HttpStatusCode.OK, mapOf("success" to true))
                } else {
                    call.respond(HttpStatusCode.NotFound, ErrorResponse("Producto no encontrado"))
                }
            } catch (e: IllegalArgumentException) {
                call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Solicitud inválida"))
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, ErrorResponse(e.message ?: "Error interno del servidor"))
            }
        }

        // Eliminar un producto
        delete("/productos/{codigo}") {
            try {
                val codigo = call.parameters["codigo"]?.toIntOrNull()
                    ?: return@delete call.respond(HttpStatusCode.BadRequest, ErrorResponse("Código inválido"))

                val success = productoController.deleteProducto(codigo)
                if (success) {
                    call.respond(HttpStatusCode.OK, mapOf("success" to true))
                } else {
                    call.respond(HttpStatusCode.NotFound, ErrorResponse("Producto no encontrado"))
                }
            } catch (e: Exception) {
                call.respond(HttpStatusCode.InternalServerError, ErrorResponse(e.message ?: "Error interno del servidor"))
            }
        }
    }
}
