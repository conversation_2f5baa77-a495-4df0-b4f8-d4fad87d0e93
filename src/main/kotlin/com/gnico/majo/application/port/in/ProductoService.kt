package com.gnico.majo.application.port.`in`

import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto

interface ProductoService {
    /**
     * Obtiene todos los productos
     */
    fun getAllProductos(): List<Producto>

    /**
     * Obtiene un producto por su código
     */
    fun getProductoByCodigo(codigo: Int): Producto?

    /**
     * Crea un nuevo producto
     * @return código del producto creado
     */
    fun createProducto(producto: Producto): Int

    /**
     * Actualiza un producto existente
     * @return true si se actualizó correctamente, false si no se encontró el producto
     */
    fun updateProducto(producto: Producto): Boolean

    /**
     * Elimina un producto por su código
     * @return true si se eliminó correctamente, false si no se encontró el producto
     */
    fun deleteProducto(codigo: Int): Boolean
}