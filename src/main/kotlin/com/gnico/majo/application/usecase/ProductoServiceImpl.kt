package com.gnico.majo.application.usecase

import com.gnico.majo.application.port.`in`.ProductoService
import com.gnico.majo.application.port.out.ProductoRepositoryPort
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto
import java.time.LocalDateTime

class ProductoServiceImpl(private val productoRepository: ProductoRepositoryPort) : ProductoService {

    override fun getAllProductos(): List<Producto> {
        return productoRepository.findAll()
    }

    override fun getProductoByCodigo(codigo: Int): Producto? {
        return productoRepository.findByCodigo(codigo)
    }

    override fun createProducto(producto: Producto): Int {
        // Verificar que el código no exista
        productoRepository.findByCodigo(producto.codigo)?.let {
            throw IllegalArgumentException("Ya existe un producto con el código ${producto.codigo}")
        }

        // Crear el producto con la fecha actual
        val productoToSave = producto.withTimestamps(
            creadoEn = LocalDateTime.now(),
            actualizadoEn = LocalDateTime.now()
        )

        return productoRepository.save(productoToSave)
    }

    override fun updateProducto(producto: Producto): Boolean {
        // Verificar que el producto exista
        val existingProducto = productoRepository.findByCodigo(producto.codigo)
            ?: throw IllegalArgumentException("No existe un producto con el código ${producto.codigo}")

        // Actualizar el producto con la fecha actual
        val productoToUpdate = producto.copy(
            actualizadoEn = LocalDateTime.now()
        )

        return productoRepository.update(productoToUpdate)
    }


    override fun deleteProducto(codigo: Int): Boolean {
        return productoRepository.delete(codigo)
    }
}