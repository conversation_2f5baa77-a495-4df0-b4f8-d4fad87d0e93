package com.gnico.majo.application.usecase

import com.gnico.majo.adapter.controller.dto.SaleItemRequest
import com.gnico.majo.application.port.`in`.SaleService
import com.gnico.majo.application.domain.model.Cliente
import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.SaleItem
import com.gnico.majo.application.domain.model.Vendedor
import com.gnico.majo.application.port.out.PrinterPort
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.SalesReportPort
import com.gnico.majo.application.port.out.SalesSummary
import com.gnico.majo.application.port.out.ClienteRepository
import com.gnico.majo.application.port.out.TipoIvaRepository
import com.gnico.majo.application.port.out.VendedorRepository
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class SaleServiceImpl(
    private val saleRepository: SaleRepositoryPort,
    private val printer: PrinterPort,
    private val salesReport: SalesReportPort,
    private val vendedorRepository: VendedorRepository,
    private val clienteRepository: ClienteRepository,
    private val tipoIvaRepository: TipoIvaRepository
) : SaleService {

    override suspend fun createSale(
        clienteId: Int?,
        vendedorId: Int,
        itemsRequest: List<SaleItemRequest>
    ): Id {
        // Obtener vendedor (requerido)
        val vendedor = vendedorRepository.findById(Id(vendedorId))
            ?: throw IllegalArgumentException("Vendedor $vendedorId no encontrado")

        // Obtener cliente (opcional)
        val cliente = clienteId?.let { id ->
            clienteRepository.findById(Id(id))
                ?: throw IllegalArgumentException("Cliente $id no encontrado")
        }

        // Mapear ítems
        val items = itemsRequest.map { item ->
            SaleItem(
                productoCodigo = item.productoId,
                cantidad = BigDecimal.valueOf(item.cantidad),
                precioUnitario = BigDecimal.valueOf(item.precioUnitario),
                tipoIva = Id(item.tipoIvaId),
                subtotal = BigDecimal.ZERO, // Calculado más adelante
                baseImp = BigDecimal.ZERO,  // Calculado más adelante
                importeIva = BigDecimal.ZERO // Calculado más adelante
            )
        }

        // Obtener tipos de IVA
        val tiposIva = tipoIvaRepository.findAll().associate { it.id to it.porcentaje }

        return createSale(cliente, vendedor, items, tiposIva)
    }

    // Metodo original renombrado a private
    private fun createSale(
        cliente: Cliente?,
        vendedor: Vendedor,
        items: List<SaleItem>,
        tiposIva: Map<Id, BigDecimal>
    ): Id {
        val numeroVenta = "V-${UUID.randomUUID().toString().substring(0, 8)}"
        var montoTotal = BigDecimal.ZERO

        // Calcular subtotales e IVA para los ítems
        val validatedItems = items.map { item ->
            val porcentajeIva = tiposIva[item.tipoIva]
                ?: throw IllegalArgumentException("Tipo IVA ${item.tipoIva.value} no encontrado")
            val subtotal = item.cantidad.multiply(item.precioUnitario)
            val baseImp = subtotal
            val importeIva = baseImp.multiply(porcentajeIva.divide(BigDecimal("100")))
            montoTotal += subtotal.add(importeIva)

            item.copy(subtotal = subtotal, baseImp = baseImp, importeIva = importeIva)
        }

        val sale = Sale(
            numeroVenta = numeroVenta,
            cliente = cliente,
            vendedor = vendedor,
            fechaVenta = LocalDateTime.now(),
            montoTotal = montoTotal,
            estado = "COMPLETADA",
            items = validatedItems
        )

        return saleRepository.saveSale(sale)
    }


    override fun createComprobante(
        ventaId: Id,
        tipoComprobante: String,
        puntoVenta: Int
    ): Id {
        val sale = saleRepository.findSaleById(ventaId)
            ?: throw IllegalArgumentException("Venta ${ventaId.value} no encontrada")

        if (tipoComprobante == "FACTURA_A" && sale.cliente == null) {
            throw IllegalArgumentException("Factura A requiere un cliente")
        }

        // Calcular importes desde los ítems
        val impNeto = sale.items.sumOf { it.baseImp }
        val impIva = sale.items.sumOf { it.importeIva }
        val impTotal = impNeto.add(impIva)
        val impTotConc = BigDecimal.ZERO
        val impTrib = BigDecimal.ZERO

        val comprobante = Comprobante(
            venta = ventaId,
            tipoComprobante = tipoComprobante,
            puntoVenta = puntoVenta,
            numeroComprobante = 0, // Será asignado por el adaptador (o WSFE)
            cae = "PENDING", // Será actualizado tras WSFE
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = impTotal,
            impTotConc = impTotConc,
            impNeto = impNeto,
            impIva = impIva,
            impTrib = impTrib,
            monId = "PES",
            monCotiz = BigDecimal("1.0"),
            estado = "PENDIENTE"
        )

        val comprobanteId = saleRepository.saveComprobante(comprobante)
        val savedComprobante = saleRepository.findComprobanteById(comprobanteId)
            ?: throw IllegalStateException("Comprobante ${comprobanteId.value} no encontrado")

        // Imprimir comprobante
        printer.printComprobante(savedComprobante, sale)

        return comprobanteId
    }

    /**
     * Obtiene un resumen de ventas con/sin comprobante.
     */
    fun getSalesSummary(startDate: LocalDateTime, endDate: LocalDateTime): SalesSummary {
        return salesReport.getSalesSummary(startDate, endDate)
    }
}