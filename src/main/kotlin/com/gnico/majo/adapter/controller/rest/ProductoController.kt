package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.adapter.controller.dto.ProductoCodigoResponse
import com.gnico.majo.adapter.controller.dto.ProductoListResponse
import com.gnico.majo.adapter.controller.dto.ProductoRequest
import com.gnico.majo.adapter.controller.dto.ProductoResponse
import com.gnico.majo.adapter.controller.dto.ProductoUpdateRequest
import com.gnico.majo.application.port.`in`.ProductoService
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto
import java.math.BigDecimal

class ProductoController(private val productoService: ProductoService) {

    fun getAllProductos(): ProductoListResponse {
        val productos = productoService.getAllProductos()
        return ProductoListResponse(
            productos = productos.map { mapToProductoResponse(it) }
        )
    }

    fun getProductoById(id: Int): ProductoResponse? {
        val producto = productoService.getProductoById(Id(id))
        return producto?.let { mapToProductoResponse(it) }
    }

    fun getProductoByCodigo(codigo: String): ProductoResponse? {
        val producto = productoService.getProductoByCodigo(codigo)
        return producto?.let { mapToProductoResponse(it) }
    }

    fun createProducto(request: ProductoRequest): ProductoIdResponse {
        val producto = mapToProducto(request)
        val id = productoService.createProducto(producto)
        return ProductoIdResponse(id.value)
    }

    fun updateProducto(request: ProductoUpdateRequest): Boolean {
        val producto = mapToProducto(request)
        return productoService.updateProducto(producto)
    }

    fun deleteProducto(id: Int): Boolean {
        return productoService.deleteProducto(Id(id))
    }

    private fun mapToProducto(request: ProductoRequest): Producto {
        return Producto(
            codigo = request.codigo,
            nombre = request.nombre,
            descripcion = request.descripcion,
            unidadMedida = Id(request.unidadMedidaId),
            tipoIva = Id(request.tipoIvaId),
            categoria = request.categoriaId?.let { Id(it) },
            precioUnitario = request.precioUnitario?.let { BigDecimal(it.toString()) },
            stockActual = request.stockActual,
            activo = request.activo
        )
    }

    private fun mapToProducto(request: ProductoUpdateRequest): Producto {
        return Producto(
            id = Id(request.id),
            codigo = request.codigo,
            nombre = request.nombre,
            descripcion = request.descripcion,
            unidadMedida = Id(request.unidadMedidaId),
            tipoIva = Id(request.tipoIvaId),
            categoria = request.categoriaId?.let { Id(it) },
            precioUnitario = request.precioUnitario?.let { BigDecimal(it.toString()) },
            stockActual = request.stockActual,
            activo = request.activo
        )
    }

    private fun mapToProductoResponse(producto: Producto): ProductoResponse {
        return ProductoResponse(
            id = producto.id!!.value,
            codigo = producto.codigo,
            nombre = producto.nombre,
            descripcion = producto.descripcion,
            unidadMedidaId = producto.unidadMedida.value,
            tipoIvaId = producto.tipoIva.value,
            categoriaId = producto.categoria?.value,
            precioUnitario = producto.precioUnitario?.toDouble(),
            stockActual = producto.stockActual,
            activo = producto.activo
        )
    }
}