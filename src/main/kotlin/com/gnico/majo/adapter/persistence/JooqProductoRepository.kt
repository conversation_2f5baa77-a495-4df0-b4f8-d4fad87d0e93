package com.gnico.majo.adapter.persistence

import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto
import com.gnico.majo.application.port.out.ProductoRepositoryPort
import com.gnico.majo.jooq.generated.tables.Productos.Companion.PRODUCTOS
import com.gnico.majo.jooq.generated.tables.records.ProductosRecord
import org.jooq.impl.DSL
import java.time.LocalDateTime

class JooqProductoRepository : ProductoRepositoryPort {
    override fun findAll(): List<Producto> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(PRODUCTOS)
                .where(PRODUCTOS.ACTIVO.eq(true))
                .fetch()
                .map { mapToProducto(it) }
        }
    }

    override fun findByCodigo(codigo: Int): Producto? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(PRODUCTOS)
                .where(PRODUCTOS.CODIGO.eq(codigo))
                .fetchOne()
                ?.let { mapToProducto(it) }
        }
    }

    override fun save(producto: Producto): Id {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val record = dsl.newRecord(PRODUCTOS).apply {
                codigo = producto.codigo
                nombre = producto.nombre
                descripcion = producto.descripcion
                unidadMedidaId = producto.unidadMedida.value
                tipoIvaId = producto.tipoIva.value
                categoriaId = producto.categoria?.value
                precioUnitario = producto.precioUnitario
                stockActual = producto.stockActual
                activo = producto.activo
                creadoEn = LocalDateTime.now()
                actualizadoEn = LocalDateTime.now()
            }
            record.store()

            Id(record.id ?: throw IllegalStateException("Error al guardar el producto"))
        }
    }

    override fun update(producto: Producto): Boolean {
        val id = producto.id?.value ?: throw IllegalArgumentException("El ID del producto no puede ser nulo")

        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val count = dsl.update(PRODUCTOS)
                .set(PRODUCTOS.CODIGO, producto.codigo)
                .set(PRODUCTOS.NOMBRE, producto.nombre)
                .set(PRODUCTOS.DESCRIPCION, producto.descripcion)
                .set(PRODUCTOS.UNIDAD_MEDIDA_ID, producto.unidadMedida.value)
                .set(PRODUCTOS.TIPO_IVA_ID, producto.tipoIva.value)
                .set(PRODUCTOS.CATEGORIA_ID, producto.categoria?.value)
                .set(PRODUCTOS.PRECIO_UNITARIO, producto.precioUnitario)
                .set(PRODUCTOS.STOCK_ACTUAL, producto.stockActual)
                .set(PRODUCTOS.ACTIVO, producto.activo)
                .set(PRODUCTOS.ACTUALIZADO_EN, LocalDateTime.now())
                .where(PRODUCTOS.ID.eq(id))
                .execute()

            count > 0
        }
    }

    override fun delete(id: Id): Boolean {
        // Soft delete: actualizar el campo activo a false
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val count = dsl.update(PRODUCTOS)
                .set(PRODUCTOS.ACTIVO, false)
                .set(PRODUCTOS.ACTUALIZADO_EN, LocalDateTime.now())
                .where(PRODUCTOS.ID.eq(id.value))
                .execute()

            count > 0
        }
    }

    private fun mapToProducto(record: ProductosRecord): Producto {
        return Producto.create(
            codigo = record.codigo!!,
            nombre = record.nombre!!,
            descripcion = record.descripcion,
            unidadMedida = Id(record.unidadMedidaId!!),
            tipoIva = Id(record.tipoIvaId!!),
            categoria = record.categoriaId?.let { Id(it) },
            precioUnitario = record.precioUnitario,
            stockActual = record.stockActual,
            activo = record.activo!!,
            creadoEn = record.creadoEn,
            actualizadoEn = record.actualizadoEn
        )
    }
}
