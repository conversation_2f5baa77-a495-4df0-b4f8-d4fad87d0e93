package com.gnico.majo.domain.repository

import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto
import com.gnico.majo.application.port.out.ProductoRepositoryPort
import java.util.concurrent.ConcurrentHashMap

/**
 * Implementación en memoria del repositorio de productos para tests
 */
class MockProductoRepository : ProductoRepositoryPort {
    private val productos = ConcurrentHashMap<Int, Producto>()

    override fun findAll(): List<Producto> {
        return productos.values.filter { it.activo }.toList()
    }

    override fun findByCodigo(codigo: Int): Producto? {
        return productos[codigo]
    }

    override fun save(producto: Producto): Int {
        productos[producto.codigo] = producto
        return producto.codigo
    }

    override fun update(producto: Producto): Boolean {
        if (!productos.containsKey(producto.codigo)) {
            return false
        }
        productos[producto.codigo] = producto
        return true
    }

    override fun delete(codigo: Int): Boolean {
        val producto = productos[codigo] ?: return false
        productos[codigo] = producto.copy(activo = false)
        return true
    }

    fun clear() {
        productos.clear()
    }
}
