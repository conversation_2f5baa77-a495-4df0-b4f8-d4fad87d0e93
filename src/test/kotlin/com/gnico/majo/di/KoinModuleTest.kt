package com.gnico.majo.di

import com.gnico.majo.adapter.controller.rest.ProductoController
import com.gnico.majo.adapter.controller.rest.SaleController
import com.gnico.majo.application.port.`in`.ProductoService
import com.gnico.majo.application.port.`in`.SaleService
import com.gnico.majo.application.port.out.PrinterPort
import com.gnico.majo.application.port.out.ProductoRepositoryPort
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.SalesReportPort
import com.gnico.majo.application.port.out.ClienteRepository
import com.gnico.majo.application.port.out.TipoIvaRepository
import com.gnico.majo.application.port.out.VendedorRepository
import com.gnico.majo.infrastructure.config.appModule
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.context.stopKoin
import org.koin.core.context.startKoin
import org.koin.test.KoinTest
import org.koin.test.get
import kotlin.test.assertNotNull

class KoinModuleTest : KoinTest {

    @BeforeEach
    fun setup() {
        startKoin {
            modules(appModule) // Replace with your Koin module(s)
        }
    }

    @AfterEach
    fun tearDown() {
        stopKoin()
    }

    @Test
    fun `verify all dependencies are created and not null`() {
        // Check repositories
        assertNotNull(get<SaleRepositoryPort>(), "SaleRepositoryPort should not be null")
        assertNotNull(get<VendedorRepository>(), "VendedorRepository should not be null")
        assertNotNull(get<ClienteRepository>(), "ClienteRepository should not be null")
        assertNotNull(get<TipoIvaRepository>(), "TipoIvaRepository should not be null")
        assertNotNull(get<SalesReportPort>(), "SalesReportPort should not be null")
        assertNotNull(get<ProductoRepositoryPort>(), "ProductoRepositoryPort should not be null")

        // Check services
        assertNotNull(get<PrinterPort>(), "PrinterPort should not be null")
        assertNotNull(get<SaleService>(), "SaleService should not be null")
        assertNotNull(get<ProductoService>(), "ProductoService should not be null")

        // Check controllers
        assertNotNull(get<SaleController>(), "SaleController should not be null")
        assertNotNull(get<ProductoController>(), "ProductoController should not be null")
    }
}
