package com.gnico.majo.api.controller

import com.gnico.majo.adapter.controller.rest.ProductoController
import com.gnico.majo.adapter.controller.dto.ProductoRequest
import com.gnico.majo.adapter.controller.dto.ProductoUpdateRequest
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto
import com.gnico.majo.application.port.`in`.ProductoService
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import java.math.BigDecimal

class ProductoControllerTest {

    private lateinit var productoService: ProductoService
    private lateinit var productoController: ProductoController

    @BeforeEach
    fun setup() {
        productoService = mock(ProductoService::class.java)
        productoController = ProductoController(productoService)
    }

    @Test
    fun `getAllProductos should return all products from service`() {
        // Arrange
        val productos = listOf(
            createTestProducto(1, "Producto 1"),
            createTestProducto(2, "Producto 2")
        )
        `when`(productoService.getAllProductos()).thenReturn(productos)

        // Act
        val response = productoController.getAllProductos()

        // Assert
        assertEquals(2, response.productos.size)
        assertEquals(1, response.productos[0].codigo)
        assertEquals("Producto 1", response.productos[0].nombre)
        assertEquals(2, response.productos[1].codigo)
        assertEquals("Producto 2", response.productos[1].nombre)
        verify(productoService, times(1)).getAllProductos()
    }

    @Test
    fun `getProductoByCodigo should return product when it exists`() {
        // Arrange
        val producto = createTestProducto(1, "Producto 1")
        `when`(productoService.getProductoByCodigo(1)).thenReturn(producto)

        // Act
        val response = productoController.getProductoByCodigo(1)

        // Assert
        assertNotNull(response)
        assertEquals(1, response?.codigo)
        assertEquals("Producto 1", response?.nombre)
        verify(productoService, times(1)).getProductoByCodigo(1)
    }

    @Test
    fun `getProductoByCodigo should return null when product does not exist`() {
        // Arrange
        `when`(productoService.getProductoByCodigo(999)).thenReturn(null)

        // Act
        val response = productoController.getProductoByCodigo(999)

        // Assert
        assertNull(response)
        verify(productoService, times(1)).getProductoByCodigo(999)
    }



    @Test
    fun `createProducto should call service and return codigo`() {
        // Arrange
        val request = ProductoRequest(
            codigo = 1,
            nombre = "Producto 1",
            descripcion = "Descripción",
            unidadMedidaId = 1,
            tipoIvaId = 1,
            categoriaId = 1,
            precioUnitario = 100.0,
            stockActual = 10
        )

        `when`(productoService.createProducto(any())).thenReturn(1)

        // Act
        val response = productoController.createProducto(request)

        // Assert
        assertEquals(1, response.codigo)
        verify(productoService, times(1)).createProducto(any())
    }

    @Test
    fun `updateProducto should call service and return result`() {
        // Arrange
        val request = ProductoUpdateRequest(
            codigo = 1,
            nombre = "Producto Actualizado",
            descripcion = "Descripción actualizada",
            unidadMedidaId = 1,
            tipoIvaId = 1,
            categoriaId = 1,
            precioUnitario = 120.0,
            stockActual = 15
        )

        `when`(productoService.updateProducto(any())).thenReturn(true)

        // Act
        val result = productoController.updateProducto(request)

        // Assert
        assertTrue(result)
        verify(productoService, times(1)).updateProducto(any())
    }

    @Test
    fun `deleteProducto should call service and return result`() {
        // Arrange
        `when`(productoService.deleteProducto(1)).thenReturn(true)

        // Act
        val result = productoController.deleteProducto(1)

        // Assert
        assertTrue(result)
        verify(productoService, times(1)).deleteProducto(1)
    }

    // Helper method to create test products
    private fun createTestProducto(
        codigo: Int,
        nombre: String,
        descripcion: String = "Descripción de prueba",
        unidadMedidaId: Int = 1,
        tipoIvaId: Int = 1,
        categoriaId: Int = 1,
        precioUnitario: BigDecimal = BigDecimal("100.00"),
        stockActual: Int = 10
    ): Producto {
        return Producto.create(
            codigo = codigo,
            nombre = nombre,
            descripcion = descripcion,
            unidadMedida = Id(unidadMedidaId),
            tipoIva = Id(tipoIvaId),
            categoria = Id(categoriaId),
            precioUnitario = precioUnitario,
            stockActual = stockActual
        )
    }
}
